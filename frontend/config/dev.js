
export default {
  mini: {},
  h5: {
    devServer: {
      port: 10086,
      host: 'localhost',
      proxy: {
        // 开发环境代理配置（如果需要）
        '/api': {
          target: 'http://localhost:8080',
          changeOrigin: true,
          pathRewrite: {
            '^/api': ''
          }
        }
      }
    },
    publicPath: '/',
    staticDirectory: 'static',
    // 开发环境优化
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'all'
          }
        }
      }
    }
  }
}
