
export default {
  mini: {
    // 小程序优化配置
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'all'
          },
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            chunks: 'all',
            reuseExistingChunk: true
          }
        }
      }
    }
  },
  h5: {
    // 生产环境优化配置
    optimization: {
      splitChunks: {
        chunks: 'all',
        maxInitialRequests: 6,
        maxAsyncRequests: 6,
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'all'
          },
          vue: {
            name: 'vue',
            test: /[\\/]node_modules[\\/]vue/,
            priority: 20,
            chunks: 'all'
          },
          taro: {
            name: 'taro',
            test: /[\\/]node_modules[\\/]@tarojs/,
            priority: 15,
            chunks: 'all'
          },
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            chunks: 'all',
            reuseExistingChunk: true
          }
        }
      },
      // 启用 Tree Shaking
      usedExports: true,
      sideEffects: false
    },
    // 压缩配置
    terser: {
      enable: true,
      config: {
        compress: {
          drop_console: true, // 移除 console
          drop_debugger: true, // 移除 debugger
          pure_funcs: ['console.log'] // 移除特定函数调用
        },
        mangle: {
          safari10: true
        },
        output: {
          comments: false // 移除注释
        }
      }
    },
    // CSS 压缩
    csso: {
      enable: true,
      config: {
        comments: false
      }
    },
    /**
     * WebpackChain 插件配置
     * @docs https://github.com/neutrinojs/webpack-chain
     */
    webpackChain(chain) {
      // 生产环境可以启用 bundle 分析
      if (process.env.ANALYZE) {
        chain.plugin('analyzer')
          .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [{
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: 'bundle-report.html'
          }])
      }

      // 优化资源加载
      chain.optimization
        .runtimeChunk('single')
        .splitChunks({
          chunks: 'all',
          cacheGroups: {
            vendor: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: 10
            }
          }
        })
    }
  }
}
