export default {
  mini: {
    // 测试环境小程序配置
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'all'
          },
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            chunks: 'all',
            reuseExistingChunk: true
          }
        }
      }
    }
  },
  h5: {
    // 测试环境H5配置
    devServer: {
      port: 10087,
      host: 'localhost',
      proxy: {
        // 测试环境代理配置
        '/api': {
          target: 'https://test-52kdj-api.wansu.tech',
          changeOrigin: true,
          pathRewrite: {
            '^/api': ''
          }
        }
      }
    },
    publicPath: '/',
    staticDirectory: 'static',
    // 测试环境优化（介于开发和生产之间）
    optimization: {
      splitChunks: {
        chunks: 'all',
        cacheGroups: {
          vendor: {
            name: 'vendor',
            test: /[\\/]node_modules[\\/]/,
            priority: 10,
            chunks: 'all'
          },
          common: {
            name: 'common',
            minChunks: 2,
            priority: 5,
            chunks: 'all',
            reuseExistingChunk: true
          }
        }
      }
    },
    // 测试环境启用部分压缩，保留调试信息
    terser: {
      enable: true,
      config: {
        compress: {
          drop_console: false, // 保留 console 用于调试
          drop_debugger: false, // 保留 debugger
          pure_funcs: [] // 不移除函数调用
        },
        mangle: false, // 不混淆变量名，便于调试
        output: {
          comments: true // 保留注释
        }
      }
    }
  }
}
