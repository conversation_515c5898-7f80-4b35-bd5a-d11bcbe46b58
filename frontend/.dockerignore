# Node modules
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Build output (will be generated during build)
dist/

# Development files
.env.development
.env.local
.env.*.local

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Git
.git/
.gitignore

# Logs
logs/
*.log

# Coverage
coverage/

# Temporary files
.tmp/
.temp/

# Cache directories
.cache/
.parcel-cache/

# Husky
.husky/