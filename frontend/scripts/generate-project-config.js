#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// 获取环境变量
const env = process.env.NODE_ENV || 'development'

// 基础配置
const baseConfig = {
  "miniprogramRoot": "./dist",
  "projectname": "52kanduanju.mp",
  "description": "52看短剧微信小程序",
  "compileType": "miniprogram"
}

// 环境特定配置
const envConfigs = {
  development: {
    appid: "wx6b8d1380d1a92670", // 开发环境使用测试appid
    setting: {
      urlCheck: false, // 开发环境关闭URL检查
      es6: false,
      enhance: false,
      compileHotReLoad: true, // 开启热重载
      postcss: false,
      minified: false, // 不压缩
      checkSiteMap: false,
      babelSetting: {
        ignore: [],
        disablePlugins: [],
        outputPath: ""
      }
    }
  },
  test: {
    appid: "wx6b8d1380d1a92670", // 测试环境使用测试appid
    setting: {
      urlCheck: true, // 测试环境开启URL检查
      es6: false,
      enhance: false,
      compileHotReLoad: false,
      postcss: false,
      minified: false, // 测试环境不压缩，便于调试
      checkSiteMap: true
    }
  },
  production: {
    appid: "wx6b8d1380d1a92670", // 生产环境需要配置正式appid
    setting: {
      urlCheck: true, // 生产环境严格检查
      es6: false,
      enhance: true, // 启用增强编译
      compileHotReLoad: false,
      postcss: false,
      minified: true, // 生产环境压缩
      checkSiteMap: true,
      coverView: true,
      lazyCodeLoading: "requiredComponents" // 启用按需注入
    }
  }
}

// 合并配置
const envConfig = envConfigs[env] || envConfigs.development
const config = {
  ...baseConfig,
  appid: envConfig.appid,
  setting: envConfig.setting
}

// 生成配置文件
const configPath = path.join(__dirname, '../project.config.json')
fs.writeFileSync(configPath, JSON.stringify(config, null, 2))

console.log(`✅ 已生成 ${env} 环境的 project.config.json`)
console.log(`📍 配置文件路径: ${configPath}`)
console.log(`🔧 AppID: ${config.appid}`)
console.log(`⚙️  压缩设置: ${config.setting.minified ? '启用' : '禁用'}`)
