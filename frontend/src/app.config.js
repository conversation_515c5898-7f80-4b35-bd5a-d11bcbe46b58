export default defineAppConfig({
  pages: [
    'pages/index/index',
    'pages/ranking/index',
    'pages/myta/index',
    'pages/search/index',
    'pages/taglist/index',
    'pages/discover/index',
    'pages/actor/index',
    'pages/drama/index'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#fff',
    navigationBarTitleText: '我爱看短剧',
    navigationBarTextStyle: 'black'
  },
  tabBar: {
    color: '#64748b',
    selectedColor: '#6366f1',
    backgroundColor: '#ffffff',
    borderStyle: 'white',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '每日短剧',
        iconPath: './assets/tabbar/movie.png',
        selectedIconPath: './assets/tabbar/movie-active.png'
      },
      {
        pagePath: 'pages/ranking/index',
        text: '找好剧',
        iconPath: './assets/tabbar/search.png',
        selectedIconPath: './assets/tabbar/search-active.png'
      },
      {
        pagePath: 'pages/myta/index',
        text: '我的Ta',
        iconPath: './assets/tabbar/person.png',
        selectedIconPath: './assets/tabbar/person-active.png'
      }
    ]
  }
})
