import Taro from '@tarojs/taro'

// API基础配置 - 使用环境变量
const API_BASE_URL = process.env.NODE_ENV === 'development'
  ? 'http://localhost:8080'
  : 'https://52kdj-api.wansu.tech'

// 通用请求方法
const request = async (url, options = {}) => {
  const { method = 'GET', data, headers = {} } = options

  try {
    const response = await Taro.request({
      url: `${API_BASE_URL}${url}`,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        'X-User-ID': '1', // 临时用户ID，后续可以从用户状态获取
        ...headers
      }
    })

    if (response.statusCode >= 200 && response.statusCode < 300) {
      return response.data
    } else {
      console.error('API请求失败:', response)
      throw new Error(`请求失败: ${response.statusCode}`)
    }
  } catch (error) {
    console.error('API请求错误:', error)
    throw error
  }
}

// API接口定义
export const api = {
  // 首页推荐
  getHomeRecommendation: () => request('/v1/recommendation/home'),

  // 搜索
  search: (params) => {
    const { keyword, type = 'all', page = 1, limit = 20 } = params
    return request(`/v1/search?keyword=${encodeURIComponent(keyword)}&type=${type}&page=${page}&limit=${limit}`)
  },

  // 短剧相关
  getDramas: (params = {}) => {
    const { page = 1, limit = 20, category, tag } = params
    let url = `/v1/dramas?page=${page}&limit=${limit}`
    if (category) url += `&category=${encodeURIComponent(category)}`
    if (tag) url += `&tag=${encodeURIComponent(tag)}`
    return request(url)
  },

  getDramaById: (dramaId) => request(`/v1/dramas/${dramaId}`),

  getDramaCuts: (dramaId, params = {}) => {
    const { page = 1, limit = 20 } = params
    return request(`/v1/dramas/${dramaId}/cuts?page=${page}&limit=${limit}`)
  },

  getDramaNews: (dramaId, params = {}) => {
    const { page = 1, limit = 20 } = params
    return request(`/v1/dramas/${dramaId}/news?page=${page}&limit=${limit}`)
  },

  // 演员相关
  getActorById: (actorId) => request(`/v1/actors/${actorId}`),

  getActorDramas: (actorId, params = {}) => {
    const { page = 1, limit = 20 } = params
    return request(`/v1/actors/${actorId}/dramas?page=${page}&limit=${limit}`)
  },

  getActorCuts: (actorId, params = {}) => {
    const { page = 1, limit = 20 } = params
    return request(`/v1/actors/${actorId}/cuts?page=${page}&limit=${limit}`)
  },

  followActor: (actorId) => request(`/v1/actors/${actorId}/follow`, { method: 'POST' }),

  unfollowActor: (actorId) => request(`/v1/actors/${actorId}/follow`, { method: 'DELETE' }),

  // 评论相关
  getComments: (params) => {
    const { contentType, contentId, page = 1, limit = 20 } = params
    return request(`/v1/comments?contentType=${contentType}&contentId=${contentId}&page=${page}&limit=${limit}`)
  },

  postComment: (data) => request('/v1/comments', { method: 'POST', data }),

  // 排行榜相关
  getTagRanking: (params = {}) => {
    const { type = 'hot', page = 1, limit = 20 } = params
    return request(`/v1/ranking/tags?type=${type}&page=${page}&limit=${limit}`)
  },

  getActorRanking: (params = {}) => {
    const { type = 'total', page = 1, limit = 20 } = params
    return request(`/v1/ranking/actors?type=${type}&page=${page}&limit=${limit}`)
  },

  // 标签相关
  getTags: (params = {}) => {
    const { page = 1, limit = 20 } = params
    return request(`/v1/tags?page=${page}&limit=${limit}`)
  },

  getTagByName: (tagName) => request(`/v1/tags/${encodeURIComponent(tagName)}`),

  getTagDramas: (tagName, params = {}) => {
    const { page = 1, limit = 20 } = params
    return request(`/v1/tags/${encodeURIComponent(tagName)}/dramas?page=${page}&limit=${limit}`)
  },

  // 用户相关
  getUserFollows: (params = {}) => {
    const { page = 1, limit = 20 } = params
    return request(`/v1/user/follows?page=${page}&limit=${limit}`)
  },

  // 支付相关
  unlockPayment: (data) => request('/v1/payment/unlock', { method: 'POST', data }),

  getPaymentStatus: (params) => {
    const { contentType, contentId } = params
    return request(`/v1/payment/status?contentType=${contentType}&contentId=${contentId}`)
  }
}

export default api
