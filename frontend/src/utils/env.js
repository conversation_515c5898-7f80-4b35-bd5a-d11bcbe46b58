/**
 * 环境配置工具类
 * 提供统一的环境判断和配置获取方法
 */

// 获取当前环境
export const getCurrentEnv = () => {
  return NODE_ENV || 'development'
}

// 获取当前平台
export const getCurrentPlatform = () => {
  return TARO_ENV || 'h5'
}

// 环境判断
export const isDev = () => getCurrentEnv() === 'development'
export const isTest = () => getCurrentEnv() === 'test'
export const isProd = () => getCurrentEnv() === 'production'

// 平台判断
export const isWeapp = () => getCurrentPlatform() === 'weapp'
export const isH5 = () => getCurrentPlatform() === 'h5'
export const isAlipay = () => getCurrentPlatform() === 'alipay'
export const isSwan = () => getCurrentPlatform() === 'swan'
export const isTt = () => getCurrentPlatform() === 'tt'

// 获取API基础地址
export const getApiBaseUrl = () => {
  return API_BASE_URL
}

// 获取应用版本
export const getAppVersion = () => {
  return APP_VERSION
}

// 环境配置对象
export const envConfig = {
  // 当前环境信息
  env: getCurrentEnv(),
  platform: getCurrentPlatform(),
  version: getAppVersion(),
  
  // API配置
  apiBaseUrl: getApiBaseUrl(),
  
  // 功能开关（根据环境控制）
  features: {
    // 是否启用调试模式
    debug: isDev() || isTest(),
    // 是否启用错误上报
    errorReport: isProd(),
    // 是否启用性能监控
    performance: isProd(),
    // 是否启用埋点统计
    analytics: isProd(),
    // 是否显示调试信息
    showDebugInfo: isDev(),
    // 是否启用mock数据
    enableMock: isDev()
  },
  
  // 小程序特定配置
  weapp: {
    // 是否启用分包加载
    enableSubpackages: isProd(),
    // 是否启用懒加载
    enableLazyLoad: isProd(),
    // 分享配置
    share: {
      title: isProd() ? '52看短剧' : `52看短剧-${getCurrentEnv()}`,
      path: '/pages/index/index',
      imageUrl: ''
    }
  },
  
  // H5特定配置
  h5: {
    // 路由模式
    routerMode: 'hash',
    // 是否启用PWA
    enablePWA: isProd(),
    // 页面标题
    title: isProd() ? '52看短剧' : `52看短剧-${getCurrentEnv()}`
  }
}

// 日志工具（根据环境控制输出）
export const logger = {
  log: (...args) => {
    if (envConfig.features.debug) {
      console.log('[LOG]', ...args)
    }
  },
  warn: (...args) => {
    if (envConfig.features.debug) {
      console.warn('[WARN]', ...args)
    }
  },
  error: (...args) => {
    console.error('[ERROR]', ...args)
  },
  debug: (...args) => {
    if (envConfig.features.showDebugInfo) {
      console.log('[DEBUG]', ...args)
    }
  }
}

// 导出默认配置
export default envConfig
