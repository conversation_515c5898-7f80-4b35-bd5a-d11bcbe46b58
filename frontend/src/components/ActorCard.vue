<template>
  <view class="actor-card">
    <image :src="avatar" class="actor-avatar" mode="aspectFill" />
    <view class="actor-info">
      <text class="actor-name">{{ name }}</text>
      <text class="actor-desc">{{ desc }}</text>
    </view>
  </view>
</template>
<script setup>
const props = defineProps({
  avatar: String,
  name: String,
  desc: String
})
</script>

<style lang="scss" src="./ActorCard.scss"></style>

