<template>
  <view class="drama-card">
    <view class="drama-cover-wrap">
      <image :src="cover" class="drama-cover" mode="aspectFill" />
      <view v-if="badge" class="drama-badge" :style="{ background: badgeColor }">{{ badge }}</view>
    </view>
    <view class="drama-info">
      <text class="drama-title">{{ title }}</text>
      <text class="drama-desc">{{ desc }}</text>
    </view>
  </view>
</template>
<script setup>
const props = defineProps({
  cover: String,
  title: String,
  desc: String,
  badge: String,
  badgeColor: {
    type: String,
    default: '#F59E0B'
  }
})
</script>

<style lang="scss" src="./DramaCard.scss"></style>

