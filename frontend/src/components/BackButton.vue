<template>
  <view class="back-button" @tap="onBackClick">
    <text class="material-icons back-icon">{{ icon }}</text>
  </view>
</template>

<script setup>
const props = defineProps({
  icon: {
    type: String,
    default: 'arrow_back_ios_new'
  }
})

const emit = defineEmits(['back-click'])

function onBackClick() {
  emit('back-click')
  
  // 默认行为：返回上一页
  if (typeof Taro !== 'undefined' && Taro.navigateBack) {
    Taro.navigateBack()
  } else if (typeof window !== 'undefined' && window.history) {
    window.history.back()
  }
}
</script>

<style lang="scss" src="./BackButton.scss"></style>
