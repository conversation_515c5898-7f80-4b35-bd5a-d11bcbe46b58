.tabbar-wrap {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 32rpx;
  max-width: 750rpx;
  margin: 0 auto;
  z-index: 100;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background: rgba(255,255,255,0.8);
  backdrop-filter: blur(12rpx);
  border-radius: 48rpx;
  box-shadow: 0 8rpx 32rpx 0 #e0e7ff44;
  padding: 0 32rpx;
  height: 104rpx;
}
.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  color: #64748b;
  font-size: 24rpx;
  transition: color 0.3s, transform 0.3s;
  font-weight: 400;
  padding: 0 8rpx;
}
.tabbar-item-active {
  color: #6366f1;
  font-weight: 600;
  transform: scale(1.08);
}
.tabbar-icon-wrap {
  background: #e0e7ff;
  border-radius: 50%;
  padding: 16rpx;
  margin-bottom: 4rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background 0.3s;
}
.tabbar-icon-active {
  background: linear-gradient(135deg, #a5b4fc 0%, #fde68a 100%);
}
.tabbar-icon {
  font-size: 36rpx;
  color: inherit;
  opacity: 0.85;
  transition: color 0.3s, opacity 0.3s;
}
.tabbar-label {
  font-size: 22rpx;
  margin-top: 2rpx;
  opacity: 0.85;
}
