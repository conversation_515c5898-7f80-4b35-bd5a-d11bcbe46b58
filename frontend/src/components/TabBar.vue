<template>
  <view class="tabbar-wrap">
    <view
      v-for="(item, idx) in tabs"
      :key="item.key"
      :class="['tabbar-item', idx === modelValue ? 'tabbar-item-active' : '']"
      @tap="onTabClick(idx)"
    >
      <view class="tabbar-icon-wrap" :class="{ 'tabbar-icon-active': idx === modelValue }">
        <text class="material-icons tabbar-icon">{{ item.icon }}</text>
      </view>
      <text class="tabbar-label">{{ item.label }}</text>
    </view>
  </view>
</template>
<script setup>
import Taro from '@tarojs/taro'

const props = defineProps({
  modelValue: Number,
  tabs: {
    type: Array,
    default: () => [
      { key: 'drama', label: '每日短剧', icon: 'movie_creation', url: '/pages/index/index' },
      { key: 'ranking', label: '找好剧', icon: 'search', url: '/pages/ranking/index' },
      { key: 'mine', label: '我的Ta', icon: 'person_outline', url: '/pages/myta/index' }
    ]
  }
})

function onTabClick(idx) {
  if (idx === props.modelValue) return
  const tab = props.tabs[idx]
  if (tab && tab.url) {
    if (typeof Taro !== 'undefined') {
      // 使用 switchTab 进行 tabBar 页面切换
      Taro.switchTab({ url: tab.url }).catch(() => {
        // 如果 switchTab 失败，尝试 navigateTo
        Taro.navigateTo({ url: tab.url })
      })
    } else if (typeof window !== 'undefined' && window.location && tab.url) {
      // H5 fallback
      window.location.hash = `#${tab.url}`
    }
  }
}
</script>

<style lang="scss" src="./TabBar.scss"></style>

