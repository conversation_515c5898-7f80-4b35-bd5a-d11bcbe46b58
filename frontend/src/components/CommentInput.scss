.comment-input-wrap {
  display: flex;
  align-items: flex-start;
  gap: 24rpx;
  margin-bottom: 32rpx;
}
.comment-input {
  flex: 1;
  background: #f3f4f6;
  border: none;
  border-radius: 999rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  outline: none;
}
.comment-input:focus {
  box-shadow: 0 0 0 4rpx #4f46e555;
}
.comment-btn {
  background: #4f46e5;
  color: #fff;
  padding: 16rpx 32rpx;
  border-radius: 999rpx;
  font-size: 28rpx;
  font-weight: 600;
  border: none;
}
