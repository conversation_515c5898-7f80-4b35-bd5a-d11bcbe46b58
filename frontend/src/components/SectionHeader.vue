<template>
  <view class="section-header">
    <view class="section-title-wrap">
      <text v-if="icon" class="material-icons section-icon" :style="{ color: iconColor }">{{ icon }}</text>
      <text class="section-title">{{ title }}</text>
    </view>
    <view v-if="showMore" class="section-more" @tap="onMoreClick">
      <text class="section-more-text">{{ moreText }}</text>
      <text class="material-icons section-more-icon">{{ moreIcon }}</text>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  icon: {
    type: String,
    default: ''
  },
  iconColor: {
    type: String,
    default: '#F59E0B'
  },
  showMore: {
    type: Boolean,
    default: false
  },
  moreText: {
    type: String,
    default: '换一换'
  },
  moreIcon: {
    type: String,
    default: 'sync'
  }
})

const emit = defineEmits(['more-click'])

function onMoreClick() {
  emit('more-click')
}
</script>

<style lang="scss" src="./SectionHeader.scss"></style>
