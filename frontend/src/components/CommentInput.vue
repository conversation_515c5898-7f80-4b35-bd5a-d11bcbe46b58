<template>
  <view class="comment-input-wrap">
    <input
      v-model="inputValue"
      class="comment-input"
      :placeholder="placeholder"
      type="text"
    />
    <button class="comment-btn" @tap="$emit('send', inputValue)">{{ btnText }}</button>
  </view>
</template>
<script setup>
import { ref } from 'vue'
const props = defineProps({
  placeholder: {
    type: String,
    default: '发一条友善的评论吧'
  },
  btnText: {
    type: String,
    default: '发布'
  }
})
const inputValue = ref('')
</script>

<style lang="scss" src="./CommentInput.scss"></style>

