<template>
  <view class="tab-switch">
    <view 
      v-for="(tab, index) in tabs" 
      :key="tab.key || index"
      :class="['tab-item', { 'tab-active': index === modelValue }]"
      @tap="onTabClick(index)"
    >
      <text class="tab-text">{{ tab.label }}</text>
    </view>
  </view>
</template>

<script setup>
const props = defineProps({
  modelValue: {
    type: Number,
    default: 0
  },
  tabs: {
    type: Array,
    required: true,
    validator: (tabs) => tabs.every(tab => tab.label)
  }
})

const emit = defineEmits(['update:modelValue', 'tab-change'])

function onTabClick(index) {
  if (index === props.modelValue) return
  
  emit('update:modelValue', index)
  emit('tab-change', {
    index,
    tab: props.tabs[index]
  })
}
</script>

<style lang="scss" src="./TabSwitch.scss"></style>
