.tab-switch {
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #E5E7EB;
  border-radius: 9999px;
  padding: 6rpx;
  margin-bottom: 24rpx;
}

.tab-item {
  flex: 1;
  padding: 16rpx 32rpx;
  text-align: center;
  border-radius: 9999px;
  transition: all 0.3s ease;
  cursor: pointer;
  
  .tab-text {
    font-size: 30rpx;
    font-weight: 600;
    color: #6B7280;
    transition: color 0.3s ease;
    line-height: 1.2;
  }
  
  &.tab-active {
    background-color: #4F46E5;
    box-shadow: 0 8rpx 12rpx -2rpx rgba(0, 0, 0, 0.1);
    
    .tab-text {
      color: white;
    }
  }
  
  &:hover:not(.tab-active) {
    background-color: rgba(79, 70, 229, 0.1);
    
    .tab-text {
      color: #4F46E5;
    }
  }
}
