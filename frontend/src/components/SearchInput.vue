<template>
  <view :class="['search-input-container', `search-input-container--${variant}`]">
    <text class="material-icons search-icon">search</text>
    <input
      :class="['search-input', `search-input--${variant}`]"
      :placeholder="placeholder"
      type="text"
      v-model="modelValue"
      @input="$emit('update:modelValue', modelValue)"
      @confirm="onConfirm"
      confirm-type="search"
    />
  </view>
</template>

<script setup>
import { ref, watch } from 'vue'
const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: '搜索演员'
  },
  variant: {
    type: String,
    default: 'default', // 'default' | 'inline'
    validator: (value) => ['default', 'inline'].includes(value)
  }
})
const emit = defineEmits(['update:modelValue', 'confirm'])
const modelValue = ref(props.modelValue)
watch(() => props.modelValue, val => { modelValue.value = val })
watch(modelValue, val => { emit('update:modelValue', val) })

// 处理确认事件（回车键）
function onConfirm() {
  emit('confirm', modelValue.value)
}
</script>

<style lang="scss" src="./SearchInput.scss"></style>