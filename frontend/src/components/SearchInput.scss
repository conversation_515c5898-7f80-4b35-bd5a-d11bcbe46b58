.search-input-container {
  position: relative;
  width: 100%;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 24rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #9CA3AF;
  z-index: 1;
  font-family: 'Material Icons';
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: 'liga';
  -webkit-font-feature-settings: 'liga';
  -webkit-font-smoothing: antialiased;
}

.search-input {
  width: 100%;
  background-color: #F3F4F6;
  border-radius: 9999px;
  padding-left: 80rpx;
  padding-right: 32rpx;
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  font-size: 28rpx;
  border: 2rpx solid transparent;
  outline: none;
  transition: all 0.3s ease;
  color: #1F2937;

  &:focus {
    border-color: #4F46E5;
    box-shadow: 0 0 0 6rpx rgba(79, 70, 229, 0.1);
  }

  &::placeholder {
    color: #9CA3AF;
  }
}

// Inline variant for search page layout
.search-input-container--inline {
  flex: 1;

  .search-icon {
    left: 24rpx;
    font-size: 32rpx;
  }
}

.search-input--inline {
  padding-left: 80rpx;
  padding-right: 32rpx;
  padding-top: 16rpx;
  padding-bottom: 16rpx;
  font-size: 28rpx;
}