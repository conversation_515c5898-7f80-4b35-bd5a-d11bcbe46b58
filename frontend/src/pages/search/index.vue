<template>
  <view class="page-search">
    <!-- Header -->
    <view class="header">
      <view class="search-row">
        <view class="back-btn" @tap="onBackClick">
          <text class="iconfont icon-arrow_back_ios_new"></text>
        </view>
        <SearchInput v-model="searchValue" placeholder="我在精神病院学斩神" variant="inline" @confirm="onSearchClick" />
        <view class="search-btn" @tap="onSearchClick">
          <text class="search-btn-text">搜索</text>
        </view>
      </view>
    </view>
    <!-- 搜索内容区域 -->
    <view class="search-main">
      <!-- 演员 -->
      <view v-if="hasSearched && searchResults.actors.length > 0" class="section">
        <SectionHeader title="演员" :show-more="true" more-text="查看全部" more-icon="chevron_right"
          @more-click="onActorMoreClick" />
        <view v-for="actor in searchResults.actors" :key="actor.id" class="actor-row" @tap="onActorClick(actor)">
          <view class="actor-cover">
            <image v-if="actor.avatar" :src="actor.avatar" class="cover-image" mode="aspectFill" />
            <text v-else class="cover-text">封面</text>
          </view>
          <view class="actor-info">
            <text class="actor-name">{{ actor.name }}</text>
            <text class="actor-desc">代表作: {{ actor.representative }}</text>
          </view>
        </view>
      </view>
      <!-- 短剧 -->
      <view v-if="hasSearched && searchResults.dramas.length > 0" class="section">
        <SectionHeader title="短剧" :show-more="true" more-text="查看全部" more-icon="chevron_right"
          @more-click="onDramaMoreClick" />
        <view class="drama-list">
          <view v-for="drama in searchResults.dramas" :key="drama.id" class="drama-row" @tap="onDramaClick(drama)">
            <view class="drama-cover">
              <image v-if="drama.cover" :src="drama.cover" class="cover-image" mode="aspectFill" />
              <text v-else class="cover-text">封面</text>
            </view>
            <view class="drama-info">
              <text class="drama-name">{{ drama.title }}</text>
              <text class="drama-meta">{{ drama.heat }} · {{ drama.episodes }}集</text>
              <text class="drama-meta">出品: {{ drama.producer }}</text>
              <view class="drama-tags">
                <text v-for="tag in drama.tags" :key="tag" class="drama-tag" @tap.stop="onTagClick(tag)">
                  {{ tag }}
                </text>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- Cut -->
      <view class="section">
        <SectionHeader title="Cut" :show-more="true" more-text="查看全部" more-icon="chevron_right"
          @more-click="onCutMoreClick" />
        <view class="cut-list">
          <view class="cut-row">
            <view class="cut-cover">
              <text class="cover-text">Cut封面</text>
              <view class="cut-play">
                <text class="iconfont icon-play_circle_outline"></text>
              </view>
            </view>
            <view class="cut-info">
              <text class="cut-title">【高能混剪】我在精神病院学<text class="primary">斩神</text>名场面</text>
              <text class="cut-meta">UP主: 剪刀手爱德华</text>
              <text class="cut-meta">2.1万播放 · 3天前</text>
            </view>
          </view>
          <view class="cut-row">
            <view class="cut-cover">
              <text class="cover-text">Cut封面</text>
              <view class="cut-play">
                <text class="iconfont icon-play_circle_outline"></text>
              </view>
            </view>
            <view class="cut-info">
              <text class="cut-title">张三演技炸裂时刻 | 我在精神病院学<text class="primary">斩神</text></text>
              <text class="cut-meta">UP主: 剧迷小分队</text>
              <text class="cut-meta">10.5万播放 · 1周前</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 新闻 -->
      <view class="section">
        <SectionHeader title="新闻" :show-more="true" more-text="查看全部" more-icon="chevron_right"
          @more-click="onNewsMoreClick" />
        <view class="news-list">
          <view class="news-item">
            <text class="news-title">《我在精神病院学<text class="primary">斩神</text>》剧组宣布开拍第二季</text>
            <text class="news-desc">备受期待的短剧《我在精神病院学斩神》第一季收官后好评如潮，今日官方正式宣布第二季已经投入拍摄，原班人马回归，预计明年上线...</text>
            <text class="news-meta">娱乐快讯 · 20小时前</text>
          </view>
          <view class="news-item">
            <text class="news-title">专访主演张三：谈《我在精神病院学<text class="primary">斩神</text>》背后的故事</text>
            <text class="news-desc">近日，我们有幸采访到了《我在精神病院学斩神》的男主角张三，他分享了拍摄过程中的趣事和对角色的理解，表示这是一个充满挑战与成长的过程。</text>
            <text class="news-meta">人物志 · 2天前</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import './index.scss'
import SearchInput from '../../components/SearchInput.vue'
import SectionHeader from '../../components/SectionHeader.vue'
import { ref } from 'vue'
import Taro from '@tarojs/taro'
import { api } from '../../utils/api'

const searchValue = ref('我在精神病院学斩神')

// 搜索结果数据
const searchResults = ref({
  actors: [],
  dramas: [],
  cuts: [],
  news: []
})

const loading = ref(false)
const hasSearched = ref(false)

// 事件处理函数
function onBackClick() {
  console.log('返回上一页')
  if (typeof Taro !== 'undefined' && Taro.navigateBack) {
    Taro.navigateBack()
  }
}

// 执行搜索
const performSearch = async (keyword) => {
  if (!keyword.trim()) return

  try {
    loading.value = true
    const response = await api.search({ keyword: keyword.trim(), type: 'all' })

    if (response.code === 200) {
      searchResults.value = {
        actors: response.data.actors || [],
        dramas: response.data.dramas || [],
        cuts: response.data.cuts || [],
        news: response.data.news || []
      }
      hasSearched.value = true
    }
  } catch (error) {
    console.error('搜索失败:', error)
    Taro.showToast({
      title: '搜索失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

function onSearchClick() {
  console.log('执行搜索:', searchValue.value)
  performSearch(searchValue.value)
}

function onActorMoreClick() {
  console.log('查看更多演员')
}

function onDramaMoreClick() {
  console.log('查看更多短剧')
}

function onCutMoreClick() {
  console.log('查看更多Cut')
}

function onNewsMoreClick() {
  console.log('查看更多新闻')
}

// 跳转到演员页面
function onActorClick(actor) {
  console.log('跳转到演员页面:', actor)
  const actorId = typeof actor === 'string' ? actor : actor.id
  const name = typeof actor === 'string' ? actor : actor.name
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/actor/index?id=${actorId}&name=${encodeURIComponent(name)}`
    })
  }
}

// 跳转到短剧详情页
function onDramaClick(drama) {
  console.log('跳转到短剧详情页:', drama)
  const dramaId = typeof drama === 'string' ? drama : drama.id
  const title = typeof drama === 'string' ? drama : drama.title
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/drama/index?id=${dramaId}&title=${encodeURIComponent(title)}`
    })
  }
}

// 跳转到标签列表页
function onTagClick(tagName) {
  console.log('跳转到标签列表页:', tagName)
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/taglist/index?tag=${encodeURIComponent(tagName)}`
    })
  }
}
</script>
