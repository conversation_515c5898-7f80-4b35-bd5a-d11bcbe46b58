<template>
  <view class="page-index">
    <!-- 自定义导航栏 -->
    <view class="custom-navbar">
      <view class="navbar-content">
        <text class="navbar-title">我爱看短剧</text>
      </view>
    </view>
    <!-- 今日推荐 -->
    <view class="today-recommend">
      <view class="today-row">
        <text class="today-title">今日运势</text>
        <text class="today-date">{{ homeData.todayFortune.date || today }}</text>
      </view>
      <view class="today-grid">
        <view class="today-item good">
          <text class="good-title">宜</text>
          <view class="good-desc">
            <text class="desc-text">{{ homeData.todayFortune.good }}</text>
          </view>
        </view>
        <view class="today-item bad">
          <text class="bad-title">忌</text>
          <view class="bad-desc">
            <text class="desc-text">{{ homeData.todayFortune.bad }}</text>
          </view>
        </view>
      </view>
    </view>
    <!-- 今日最火 -->
    <view class="hot-section">
      <view class="hot-header">
        <text class="hot-title">🔥 今日最火</text>
        <text class="material-icons hot-more" @tap="onDiscoverClick">chevron_right</text>
      </view>
      <!-- 推荐内容 2列 -->
      <template v-for="(recommendation, index) in homeData.recommendations" :key="index">
        <view class="hot-main-grid">
          <!-- 短剧 -->
          <view class="hot-main-item" @tap="onDramaClick(recommendation.drama)">
            <view class="hot-main-cover">
              <image v-if="getDramaCover(recommendation.drama)" :src="getDramaCover(recommendation.drama)"
                class="cover-image" mode="aspectFill" />
              <text v-else class="cover-text">封面</text>
              <text v-if="getDramaTag(recommendation.drama)" class="hot-tag tag-red">{{
                getDramaTag(recommendation.drama) }}</text>
            </view>
            <text class="hot-main-name">{{ getDramaTitle(recommendation.drama) }}</text>
            <text class="hot-main-info">{{ getDramaHeat(recommendation.drama) }}</text>
          </view>
          <!-- 演员 -->
          <view class="hot-main-item" @tap="onActorClick(recommendation.actor)">
            <view class="hot-main-cover">
              <image v-if="getActorAvatar(recommendation.actor)" :src="getActorAvatar(recommendation.actor)"
                class="cover-image" mode="aspectFill" />
              <text v-else class="cover-text">头像</text>
              <text class="hot-tag tag-green">{{ getActorGenderTag(recommendation.actor) }}</text>
            </view>
            <text class="hot-main-name">{{ getActorName(recommendation.actor) }}</text>
            <text class="hot-main-info">{{ getActorHeat(recommendation.actor) }}</text>
          </view>
        </view>
      </template>
    </view>
    <view class="discover-btn-wrap">
      <button class="discover-btn" @tap="onDiscoverClick">发现更多精彩短剧</button>
    </view>
  </view>
</template>
<script setup>
import './index.scss'
import Taro from '@tarojs/taro'
import { ref, onMounted } from 'vue'
import { api } from '../../utils/api'

// 响应式数据
const homeData = ref({
  todayFortune: {
    date: '',
    good: '看甜甜的恋爱',
    bad: '沉溺于痛苦'
  },
  recommendations: []
})

const loading = ref(false)

// Taro + Vue3 获取今天日期，格式 MM/DD
const today = (() => {
  const d = new Date()
  const mm = String(d.getMonth() + 1).padStart(2, '0')
  const dd = String(d.getDate()).padStart(2, '0')
  return `${mm}/${dd}`
})()

// 加载首页数据
const loadHomeData = async () => {
  try {
    loading.value = true
    const response = await api.getHomeRecommendation()
    if (response.code === 200) {
      homeData.value = response.data
    }
  } catch (error) {
    console.error('加载首页数据失败:', error)
    Taro.showToast({
      title: '加载数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 跳转到短剧详情页
function onDramaClick(drama) {
  console.log('跳转到短剧详情页:', drama)
  const dramaId = typeof drama === 'string' ? drama : drama.id
  const title = typeof drama === 'string' ? drama : drama.title
  Taro.navigateTo({
    url: `/pages/drama/index?id=${dramaId}&title=${encodeURIComponent(title)}`
  })
}

// 跳转到演员页面
function onActorClick(actor) {
  console.log('跳转到演员页面:', actor)
  const actorId = typeof actor === 'string' ? actor : actor.id
  const name = typeof actor === 'string' ? actor : actor.name
  Taro.navigateTo({
    url: `/pages/actor/index?id=${actorId}&name=${encodeURIComponent(name)}`
  })
}

// 跳转到找好剧页面（tabbar页面）- 热剧榜
function onDiscoverClick() {
  console.log('跳转到找好剧页面-热剧榜')
  Taro.switchTab({
    url: '/pages/ranking/index'
  })
}

// 辅助函数：获取短剧封面
const getDramaCover = (drama) => {
  // 根据后端返回的数据结构调整
  return drama?.cover || null
}

// 辅助函数：获取短剧标题
const getDramaTitle = (drama) => {
  return drama?.bookName || '未知短剧'
}

// 辅助函数：获取短剧标签
const getDramaTag = (drama) => {
  if (drama?.categories && drama.categories.length > 0) {
    return drama.categories[0].name
  }
  return null
}

// 辅助函数：获取短剧热度
const getDramaHeat = (drama) => {
  if (drama?.heatCount) {
    return `${Math.floor(drama.heatCount / 10000)}万人正在看`
  }
  return '热度未知'
}

// 辅助函数：获取演员头像
const getActorAvatar = (actor) => {
  return actor?.avatarURL || null
}

// 辅助函数：获取演员姓名
const getActorName = (actor) => {
  return actor?.name || '未知演员'
}

// 辅助函数：获取演员性别标签
const getActorGenderTag = (actor) => {
  return actor?.gender === '女' ? '女主' : '男主'
}

// 辅助函数：获取演员热度
const getActorHeat = (actor) => {
  if (actor?.actInCounts) {
    return `参演${actor.actInCounts}部作品`
  }
  return '作品数未知'
}

// 页面加载时获取数据
onMounted(() => {
  loadHomeData()
})
</script>
