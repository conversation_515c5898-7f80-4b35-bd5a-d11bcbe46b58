.page-index {
  min-height: 100vh;
  background: #f3f4f6;
  box-sizing: border-box;
  padding-bottom: 40rpx;
  // 让页面内容延伸到状态栏区域，但不需要额外的padding-top，因为我们有自定义导航栏
}

// 自定义导航栏样式
.custom-navbar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 1000;
  background: #f3f4f6; // 与页面背景色一致
  padding-top: env(safe-area-inset-top); // 状态栏高度
}

.navbar-content {
  height: 88rpx; // 导航栏内容高度
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 32rpx;
}

.navbar-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
}

.today-recommend {
  margin: calc(env(safe-area-inset-top) + 88rpx + 32rpx) 32rpx 0 32rpx; // 导航栏高度 + 间距
  padding: 32rpx;
  border-radius: 24rpx;
  background: linear-gradient(135deg, #e0e7ff 0%, #fef3c7 100%);
  box-shadow: 0 4rpx 24rpx 0 #e0e7ff44;
}

.today-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
}

.today-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
}

.today-date {
  font-size: 36rpx;
  color: #64748b;
  font-weight: 300;
}

.today-grid {
  display: flex;
  gap: 24rpx;
}

.today-item {
  flex: 1;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 16rpx;
  box-shadow: 0 2rpx 8rpx 0 #e0e7ff22 inset;
  padding: 32rpx;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
}

.good-title {
  color: #16a34a;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.bad-title {
  color: #dc2626;
  font-size: 48rpx;
  font-weight: bold;
  margin-bottom: 16rpx;
}

.good-desc,
.bad-desc {
  display: flex;
  align-items: center;

  .desc-text {
    color: #374151;
    font-size: 28rpx;
    margin-right: 8rpx;
  }

  .desc-icon {
    font-size: 24rpx;
    font-family: "Material Icons";
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    font-feature-settings: "liga";
    -webkit-font-feature-settings: "liga";
    -webkit-font-smoothing: antialiased;
  }
}

.good-desc .desc-icon {
  color: #ef4444;
}

.bad-desc .desc-icon {
  color: #9ca3af;
}

.iconfont {
  font-family: "Material Icons", "iconfont", sans-serif;
  font-size: 28rpx;
  margin-left: 8rpx;
}

.today-item .iconfont {
  font-size: 24rpx;
}

.hot-section {
  margin: 48rpx 32rpx 0 32rpx;
  padding: 32rpx;
  border-radius: 24rpx;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8rpx);
}

.hot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.hot-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #2d3748;
  display: flex;
  align-items: center;
}

.hot-more {
  font-size: 32rpx;
  color: #9ca3af;
  font-family: "Material Icons";
  font-weight: normal;
  font-style: normal;
  line-height: 1;
  letter-spacing: normal;
  text-transform: none;
  display: inline-block;
  white-space: nowrap;
  word-wrap: normal;
  direction: ltr;
  font-feature-settings: "liga";
  -webkit-font-feature-settings: "liga";
  -webkit-font-smoothing: antialiased;
}

// 主要推荐区域 (2列)
.hot-main-grid {
  display: flex;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.hot-main-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.hot-main-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 3/4;
  background: #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hot-main-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #2d3748;
  line-height: 1.3;
}

.hot-main-info {
  font-size: 28rpx;
  color: #64748b;
}

// 小卡片区域 (3列)
.hot-small-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 32rpx;
}

.hot-small-item {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.hot-small-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 3/4;
  background: #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.hot-small-name {
  font-size: 28rpx;
  font-weight: 600;
  color: #2d3748;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.hot-small-info {
  font-size: 24rpx;
  color: #9ca3af;
}

.hot-small-tag {
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  color: white;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
}

.hot-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 8rpx;
}

.hot-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 3/4;
  background: #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.cover-text {
  color: #6b7280;
  font-size: 28rpx;
}

.cover-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.hot-tag {
  position: absolute;
  top: 16rpx;
  left: 16rpx;
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  padding: 4rpx 16rpx;
  border-radius: 12rpx;
}

.tag-red {
  background: #ef4444;
}

.tag-green {
  background: #22c55e;
}

.tag-yellow {
  background: #eab308;
}

.tag-purple {
  background: #a21caf;
}

.tag-blue {
  background: #2563eb;
}

.hot-name {
  color: #2d3748;
  font-size: 28rpx;
  font-weight: bold;
}

.hot-info {
  color: #64748b;
  font-size: 24rpx;
}

.hot-info-gray {
  color: #a1a1aa;
}

.discover-btn-wrap {
  text-align: center;
  margin: 64rpx 32rpx 60rpx 32rpx;
  /* 底部留白加大，避免贴紧TabBar */
  display: flex;
  justify-content: center;
}

.discover-btn {
  width: 50vw;
  min-width: 240rpx;
  max-width: 420rpx;
  background: linear-gradient(90deg, #a5b4fc 0%, #fde68a 100%);
  /* 调淡色彩 */
  color: #374151;
  font-weight: 500;
  font-size: 30rpx;
  padding: 20rpx 0;
  border-radius: 999rpx;
  box-shadow: 0 4rpx 16rpx 0 #a5b4fc22;
  transition: box-shadow 0.3s, transform 0.3s;
}

.discover-btn:active {
  box-shadow: 0 16rpx 48rpx 0 #f59e0b33;
  transform: translateY(-4rpx);
}
