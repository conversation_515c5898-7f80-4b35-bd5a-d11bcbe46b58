<template>
  <view class="page-discover">
    <!-- Header -->
    <view class="header">
      <TabSwitch v-model="currentTab" :tabs="tabOptions" @tab-change="onTabChange" />
      <SearchInput v-model="searchValue" placeholder="搜索剧名、演员、题材" />
    </view>

    <!-- Main Content -->
    <view class="main-content">
      <!-- 热门标签 -->
      <view class="section">
        <SectionHeader title="热门标签" icon="whatshot" icon-color="#EF4444" :show-more="true" more-text="换一换"
          more-icon="sync" @more-click="onRefreshTags" />
        <view class="hot-tags-grid">
          <view v-for="tag in hotTags" :key="tag.id" class="hot-tag-item" @tap="onTagClick(tag)">
            <view class="tag-cover">
              <text class="cover-text">封面</text>
              <view class="tag-overlay"></view>
            </view>
            <view class="tag-info">
              <text class="tag-name">#{{ tag.name }}</text>
              <text class="tag-heat">{{ tag.heat }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 分类内容 -->
      <view v-for="category in categories" :key="category.id" class="section">
        <SectionHeader :title="`#${category.name}`" :show-more="true" more-text="更多" more-icon="chevron_right"
          @more-click="onCategoryMoreClick(category)" />
        <view class="category-grid">
          <view v-for="drama in category.dramas" :key="drama.id" class="drama-item" @tap="onDramaClick(drama)">
            <view class="drama-cover">
              <text class="cover-text">封面</text>
              <view class="drama-overlay">
                <text class="material-icons play-icon">play_circle_outline</text>
              </view>
              <view class="drama-heat">
                <text class="material-icons heat-icon">local_fire_department</text>
                <text class="heat-text">{{ drama.heat }}</text>
              </view>
            </view>
            <text class="drama-title">{{ drama.title }}</text>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script setup>
import './index.scss'
import { ref } from 'vue'
import SearchInput from '../../components/SearchInput.vue'
import TabSwitch from '../../components/TabSwitch.vue'
import SectionHeader from '../../components/SectionHeader.vue'
import Taro from '@tarojs/taro'

const searchValue = ref('')
const currentTab = ref(0) // 默认选中热剧榜（第一个tab）

// Tab选项
const tabOptions = ref([
  { key: 'hot', label: '热剧榜' },
  { key: 'weird', label: '猎奇榜单' }
])

// 热门标签数据
const hotTags = ref([
  { id: 1, name: '霸道总裁', heat: '2.1亿热度' },
  { id: 2, name: '重生逆袭', heat: '1.8亿热度' },
  { id: 3, name: '先婚后爱', heat: '1.5亿热度' },
  { id: 4, name: '手撕绿茶', heat: '1.2亿热度' }
])

// 分类数据
const categories = ref([
  {
    id: 1,
    name: '霸道总裁',
    dramas: [
      { id: 1, title: '霸道总裁别爱我', heat: '1022.1w' },
      { id: 2, title: '总裁的替身新娘', heat: '856.3w' },
      { id: 3, title: '豪门总裁的小娇妻', heat: '743.2w' },
      { id: 4, title: '冷酷总裁的甜心', heat: '621.8w' },
      { id: 5, title: '霸道总裁爱上我', heat: '589.4w' },
      { id: 6, title: '总裁的秘密情人', heat: '456.7w' }
    ]
  },
  {
    id: 2,
    name: '重生逆袭',
    dramas: [
      { id: 7, title: '重生之我要当学霸', heat: '892.5w' },
      { id: 8, title: '重生豪门千金', heat: '756.2w' },
      { id: 9, title: '重生之商业帝国', heat: '634.8w' },
      { id: 10, title: '重生复仇记', heat: '523.1w' },
      { id: 11, title: '重生之娱乐圈女王', heat: '467.9w' },
      { id: 12, title: '重生之医妃倾天下', heat: '398.6w' }
    ]
  }
])

// 事件处理函数
function onTabChange(event) {
  console.log('Tab changed:', event)
}

function onRefreshTags() {
  console.log('刷新热门标签')
  // 可以重新加载热门标签数据
}

function onTagClick(tag) {
  console.log('标签页面暂时不可用:', tag)
  if (typeof Taro !== 'undefined' && Taro.showToast) {
    Taro.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  }
}

function onCategoryMoreClick(category) {
  console.log('分类页面暂时不可用:', category)
  if (typeof Taro !== 'undefined' && Taro.showToast) {
    Taro.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  }
}

function onDramaClick(drama) {
  console.log('跳转到短剧详情页:', drama)
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/drama/index?title=${encodeURIComponent(drama.title)}`
    })
  }
}
</script>
