.page-discover {
  min-height: 100vh;
  background-color: #F3F4F6;
  padding-bottom: 120rpx;
}

.header {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  z-index: 10;
  padding: 24rpx 32rpx 16rpx 32rpx;
}

.main-content {
  padding: 32rpx;
  padding-top: 32rpx;
}

.section {
  margin-bottom: 48rpx;
}

// 热门标签网格
.hot-tags-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 32rpx;
}

.hot-tag-item {
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  aspect-ratio: 16/10;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
}

.tag-cover {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #D1D5DB;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .cover-text {
    color: #6B7280;
    font-size: 32rpx;
  }
}

.tag-overlay {
  position: absolute;
  inset: 0;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.7), transparent);
}

.tag-info {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx;
  color: white;
}

.tag-name {
  font-size: 32rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 8rpx;
}

.tag-heat {
  font-size: 24rpx;
  opacity: 0.8;
}

// 分类网格
.category-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 24rpx 16rpx;
}

.drama-item {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  cursor: pointer;
  transition: transform 0.3s ease;
  
  &:hover {
    transform: scale(1.02);
  }
}

.drama-cover {
  position: relative;
  width: 100%;
  aspect-ratio: 3/4;
  background-color: #D1D5DB;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  
  .cover-text {
    color: #6B7280;
    font-size: 28rpx;
  }
}

.drama-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
  
  .play-icon {
    font-size: 80rpx;
    color: white;
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    font-feature-settings: 'liga';
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
  }
}

.drama-item:hover .drama-overlay {
  opacity: 1;
}

.drama-heat {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 12rpx;
  background: linear-gradient(to top, rgba(0, 0, 0, 0.6), transparent);
  display: flex;
  align-items: center;
  color: white;
  font-size: 24rpx;
  font-weight: 600;
  
  .heat-icon {
    font-size: 24rpx;
    margin-right: 8rpx;
    font-family: 'Material Icons';
    font-weight: normal;
    font-style: normal;
    line-height: 1;
    letter-spacing: normal;
    text-transform: none;
    display: inline-block;
    white-space: nowrap;
    word-wrap: normal;
    direction: ltr;
    font-feature-settings: 'liga';
    -webkit-font-feature-settings: 'liga';
    -webkit-font-smoothing: antialiased;
  }
  
  .heat-text {
    font-size: 24rpx;
  }
}

.drama-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1F2937;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
