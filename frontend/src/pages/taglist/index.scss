.page-taglist {
  min-height: 100vh;
  background: #fff;
  box-sizing: border-box;
  padding-bottom: 120rpx;
  display: flex;
  flex-direction: column;
}
.taglist-header {
  padding: 32rpx;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 32rpx;
}

.search-btn {
  padding: 16rpx 24rpx;
  background-color: #4F46E5;
  border-radius: 9999px;
  cursor: pointer;
  transition: all 0.3s ease;

  &:hover {
    background-color: #4338CA;
  }

  &:active {
    transform: scale(0.95);
  }
}

.search-btn-text {
  color: white;
  font-size: 28rpx;
  font-weight: 600;
}

.tag-info {
  text-align: center;
  margin-bottom: 16rpx;
}

.tag-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #1F2937;
  display: block;
  margin-bottom: 8rpx;
}

.tag-desc {
  font-size: 28rpx;
  color: #6B7280;
}
.back-btn {
  color: #64748b;
  font-size: 36rpx;
  padding: 8rpx;
  background: none;
  border: none;
}
.iconfont {
  font-family: 'Material Icons', 'iconfont', sans-serif;
}
.icon-arrow_back_ios_new::before {
  content: "arrow_back_ios_new";
}
.taglist-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #2d3748;
  margin-left: 16rpx;
}
.taglist-main {
  flex: 1;
  overflow-y: auto;
  padding: 32rpx 16rpx 0 16rpx; // 左右间距缩小，防止溢出
}
.taglist-grid {
  display: grid;
  grid-template-columns: repeat(2, minmax(0, 1fr)); // 宽度自适应填充
  gap: 32rpx 24rpx;
}
.taglist-item {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-width: 0; // 防止内容撑破
}
.taglist-cover {
  width: 100%;
  aspect-ratio: 3/4;
  background: #e5e7eb;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}
.cover-text {
  color: #6b7280;
  font-size: 24rpx;
  white-space: nowrap;
}
.taglist-name {
  color: #2d3748;
  font-size: 24rpx;
  font-weight: 600;
  margin-top: 4rpx;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.taglist-info {
  color: #a1a1aa;
  font-size: 20rpx;
}
.taglist-bottom {
  text-align: center;
  color: #a1a1aa;
  font-size: 24rpx;
  padding: 64rpx 0 32rpx 0;
}
