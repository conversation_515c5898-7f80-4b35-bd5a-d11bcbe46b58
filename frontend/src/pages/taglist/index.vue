<template>
  <view class="page-taglist">
    <!-- 顶部栏 -->
    <view class="taglist-header">
      <view class="header-nav">
        <BackButton @back-click="onBackClick" />
        <SearchInput
          v-model="searchValue"
          placeholder="搜索短剧"
          variant="inline"
        />
        <view class="search-btn" @tap="onSearchClick">
          <text class="search-btn-text">搜索</text>
        </view>
      </view>
      <view class="tag-info">
        <text class="tag-title">#{{ tagName }}</text>
        <text class="tag-desc">{{ tagDesc }}</text>
      </view>
    </view>
    <scroll-view class="taglist-main" scroll-y>
      <view class="taglist-grid">
        <!-- 每行2列，宽度自适应填充 -->
        <view class="taglist-item" v-for="drama in dramas" :key="drama.title" @tap="onDramaClick(drama)">
          <view class="taglist-cover">
            <text class="cover-text">封面</text>
          </view>
          <text class="taglist-name">{{ drama.title }}</text>
          <text class="taglist-info">{{ drama.playCount }}</text>
        </view>
      </view>
      <view class="taglist-bottom">
        已经到底了
      </view>
    </scroll-view>
  </view>
</template>
<script setup>
import './index.scss'
import BackButton from '../../components/BackButton.vue'
import SearchInput from '../../components/SearchInput.vue'
import { ref } from 'vue'
import Taro from '@tarojs/taro'

const searchValue = ref('')

const tagName = '爽文'
const tagDesc = '精彩爽文短剧，让你一次看个够'

// 事件处理函数
function onBackClick() {
  console.log('返回上一页')
}

function onSearchClick() {
  console.log('搜索:', searchValue.value)
  if (searchValue.value.trim()) {
    if (typeof Taro !== 'undefined' && Taro.navigateTo) {
      Taro.navigateTo({
        url: `/pages/search/index?keyword=${encodeURIComponent(searchValue.value)}`
      })
    }
  }
}

// 跳转到短剧详情页
function onDramaClick(drama) {
  console.log('跳转到短剧详情页:', drama)
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/drama/index?title=${encodeURIComponent(drama.title)}`
    })
  }
}
const dramas = [
  { title: '七个姐姐逼我继承亿万家产', playCount: '1.2亿次播放' },
  { title: '龙王令', playCount: '9875万次播放' },
  { title: '盖世神医', playCount: '8521万次播放' },
  { title: '无双战神', playCount: '7654万次播放' },
  { title: '都市狂龙', playCount: '6987万次播放' },
  { title: '神豪的快乐生活', playCount: '6543万次播放' },
  { title: '我的总裁老婆', playCount: '5987万次播放' },
  { title: '巅峰赘婿', playCount: '5521万次播放' },
  { title: '一胎八宝：妈咪是大佬', playCount: '5123万次播放' }
]
</script>
