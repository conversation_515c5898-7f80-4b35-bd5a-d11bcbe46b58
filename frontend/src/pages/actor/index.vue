<template>
  <view class="page-actor">
    <!-- Main Content -->
    <view class="main-content">
      <!-- 演员信息 -->
      <view class="actor-info">
        <view class="actor-profile">
          <view class="actor-avatar">
            <image v-if="actorInfo.avatarUrl" :src="actorInfo.avatarUrl" class="actor-image" mode="aspectFill" />
            <text v-else class="avatar-placeholder">头像</text>
          </view>
          <view class="actor-details">
            <view class="actor-name-row">
              <text class="actor-name">{{ actorInfo.name }}</text>
              <view :class="['gender-badge', actorInfo.gender === '女' ? 'gender-female' : 'gender-male']">
                <text class="gender-icon">{{ actorInfo.gender === '女' ? '♀' : '♂' }}</text>
                <text class="gender-text">{{ actorInfo.gender }}</text>
              </view>
            </view>
            <view class="actor-heat">
              <text class="material-icons heat-icon">local_fire_department</text>
              <text class="heat-text">热度: {{ actorInfo.heat }}</text>
            </view>
            <text class="actor-works">代表作: {{ actorInfo.representative }}</text>
            <view class="social-links">
              <view class="social-link weibo">
                <text class="iconfont">微</text>
              </view>
              <view class="social-link tiktok">
                <text class="iconfont">抖</text>
              </view>
              <view class="social-link xiaohongshu">
                <text class="iconfont">小</text>
              </view>
              <view class="social-link weixin">
                <text class="iconfont">微</text>
              </view>
            </view>
            <view>
              <button class="follow-button" :class="{ 'followed': isFollowed }" @tap="onFollowClick">
                <text class="material-icons follow-icon">{{ isFollowed ? 'check' : 'add' }}</text>
                <text class="follow-text">{{ isFollowed ? '已关注' : '关注' }}</text>
              </button>
            </view>
          </view>
        </view>
      </view>

      <!-- 演员简介 -->
      <view class="actor-bio-section">
        <text class="section-title">演员简介</text>
        <text class="bio-content">{{ actorInfo.bio }}</text>
      </view>

      <!-- 参演短剧 -->
      <view class="drama-section">
        <text class="section-title">参演短剧</text>
        <view class="drama-grid">
          <view v-for="drama in displayedDramas" :key="drama.id" class="drama-item" @tap="onDramaClick(drama)">
            <view class="drama-cover">
              <image :src="drama.cover" class="drama-image" mode="aspectFill" />
              <view class="drama-overlay">
                <text class="material-icons play-icon">play_circle_filled</text>
              </view>
            </view>
            <text class="drama-title">{{ drama.title }}</text>
          </view>
        </view>
        <view v-if="hasMoreDramas" class="drama-more" @tap="onLoadMoreDramas">
          <text class="more-text">查看更多短剧 ({{ dramaList.length - displayedDramasCount }})</text>
          <text class="material-icons more-icon">expand_more</text>
        </view>
      </view>

      <!-- 精彩Cut -->
      <view class="cut-section">
        <SectionHeader title="精彩Cut" :show-more="false" />
        <view class="cut-grid-container">
          <view class="cut-grid">
            <view v-for="cut in cutsList" :key="cut.id" class="cut-item" @tap="onCutClick(cut)">
              <view class="cut-cover">
                <text class="cover-text">封面</text>
                <view class="cut-overlay">
                  <text class="material-icons play-icon">play_circle_filled</text>
                </view>
              </view>
              <text class="cut-title">{{ cut.title }}</text>
            </view>
          </view>

          <!-- 整个Cut区域的付费解锁遮罩 -->
          <view v-if="hasPaidContent" class="cut-area-pay-mask" @tap="onPaidContentClick">
            <view class="pay-content">
              <text class="material-icons lock-icon">lock</text>
              <text class="pay-text">付费解锁精彩内容</text>
              <text class="pay-subtitle">解锁后可观看所有精彩片段</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 精彩评论 -->
      <view class="comment-section">
        <SectionHeader title="精彩评论" :show-more="false" />

        <!-- 评论输入 -->
        <view class="comment-input" @tap="onCommentInputClick">
          <view class="comment-field-placeholder">
            <text class="placeholder-text">发一条友善的评论吧</text>
          </view>
          <view class="comment-submit">
            <text class="submit-text">发布</text>
          </view>
        </view>

        <!-- 评论列表 -->
        <view class="comment-list">
          <view v-for="comment in displayedComments" :key="comment.id" class="comment-item">
            <view class="comment-avatar">
              <text class="avatar-text">头像</text>
            </view>
            <view class="comment-content">
              <text class="comment-author">{{ comment.author }}</text>
              <text class="comment-text">{{ comment.content }}</text>
              <text class="comment-time">{{ comment.time }}</text>
            </view>
          </view>
        </view>

        <view v-if="hasMoreComments" class="comment-more" @tap="onLoadMoreComments">
          <text class="more-text">查看更多评论 ({{ remainingCommentsCount }})</text>
          <text class="material-icons more-icon">expand_more</text>
        </view>
      </view>
    </view>

    <!-- 评论弹窗 -->
    <view v-if="showCommentModal" class="comment-modal-overlay" @tap="onCloseCommentModal">
      <view class="comment-modal" @tap.stop>
        <view class="modal-header">
          <text class="modal-title">发表评论</text>
          <view class="modal-close" @tap="onCloseCommentModal">
            <text class="material-icons">close</text>
          </view>
        </view>
        <view class="modal-content">
          <textarea class="comment-textarea" v-model="commentText" placeholder="发一条友善的评论吧..." maxlength="200"
            auto-focus />
          <view class="comment-count">{{ commentText.length }}/200</view>
        </view>
        <view class="modal-footer">
          <view class="modal-cancel" @tap="onCloseCommentModal">
            <text>取消</text>
          </view>
          <view class="modal-submit" @tap="onSubmitComment">
            <text>发布</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部固定按钮 -->
    <view class="bottom-actions">
      <button class="action-button share-button">
        <text class="material-icons">share</text>
        <text class="button-text">分享演员</text>
      </button>
    </view>
  </view>
</template>

<script setup>
import './index.scss'
import { ref, computed, onMounted } from 'vue'
import Taro from '@tarojs/taro'
import SectionHeader from '../../components/SectionHeader.vue'
import { api } from '../../utils/api'

// 时间格式化函数
const formatTime = (dateString) => {
  if (!dateString) return '未知时间'

  const now = new Date()
  const date = new Date(dateString)
  const diff = now - date

  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))

  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else if (days < 7) {
    return `${days}天前`
  } else {
    return date.toLocaleDateString()
  }
}

const isFollowed = ref(false)
const displayedDramasCount = ref(4) // 初始显示4个（2行2列）
const loading = ref(false)

// 从路由参数获取演员ID
const actorId = ref('')
const actorName = ref('')

// 加载演员数据
const loadActorData = async () => {
  try {
    loading.value = true
    const response = await api.getActorById(actorId.value)
    if (response.code === 200) {
      const data = response.data
      // 映射API响应到前端数据结构
      actorInfo.value = {
        name: data.name || '',
        gender: data.gender || '',
        heat: data.actInCounts ? `${data.actInCounts}部作品` : '',
        representative: '', // 需要从作品列表中计算
        bio: data.desc || '暂无简介',
        avatarUrl: data.avatarUrl || '',
        platform: data.platform || '',
        actInCounts: data.actInCounts || 0,
        socialLinks: data.socialLinks
      }
      isFollowed.value = data.isFollowed || false
    }
  } catch (error) {
    console.error('加载演员数据失败:', error)
    Taro.showToast({
      title: '加载数据失败',
      icon: 'none'
    })
  } finally {
    loading.value = false
  }
}

// 加载演员作品
const loadActorDramas = async () => {
  try {
    const response = await api.getActorDramas(actorId.value)
    if (response.code === 200) {
      const dramas = response.data.duanjus || []
      // 映射API响应到前端数据结构
      dramaList.value = dramas.map(drama => ({
        id: drama.id,
        title: drama.bookName || '未知作品',
        cover: '', // TODO: 需要添加封面字段
        readCount: drama.readCount || 0
      }))

      // 更新演员的代表作品
      if (dramas.length > 0) {
        const topDramas = dramas.slice(0, 2).map(d => d.bookName).join('、')
        actorInfo.value.representative = `《${topDramas}》`
      }
    }
  } catch (error) {
    console.error('加载演员作品失败:', error)
  }
}

// 加载评论
const loadComments = async () => {
  try {
    const response = await api.getComments({
      contentType: 'actor',
      contentId: actorId.value
    })
    if (response.code === 200) {
      const comments = response.data.comments || []
      // 映射API响应到前端数据结构
      commentList.value = comments.map(comment => ({
        id: comment.id,
        author: comment.user?.nickname || comment.user?.username || '匿名用户',
        content: comment.content,
        time: formatTime(comment.created_at),
        avatar: comment.user?.avatar || ''
      }))
      totalComments.value = response.data.total || comments.length
    }
  } catch (error) {
    console.error('加载评论失败:', error)
  }
}

// 获取路由参数并加载数据
onMounted(() => {
  const instance = Taro.getCurrentInstance()
  if (instance.router && instance.router.params) {
    actorId.value = instance.router.params.id || ''
    actorName.value = decodeURIComponent(instance.router.params.name || '')

    if (actorId.value) {
      loadActorData()
      loadActorDramas()
      loadComments()
    }
  }
})

// 评论相关状态
const commentText = ref('')
const totalComments = ref(1234)
const showCommentModal = ref(false)
const displayedCommentsCount = ref(3) // 初始显示3条评论

// 计算显示的短剧列表
const displayedDramas = computed(() => {
  return dramaList.value.slice(0, displayedDramasCount.value)
})

// 计算是否还有更多短剧
const hasMoreDramas = computed(() => {
  return displayedDramasCount.value < dramaList.value.length
})

// 演员信息
const actorInfo = ref({
  name: '',
  gender: '',
  heat: '',
  representative: '',
  bio: '',
  avatarUrl: '',
  platform: '',
  actInCounts: 0,
  socialLinks: null
})

// 参演短剧列表
const dramaList = ref([])

// 精彩Cut列表
const cutsList = ref([
  {
    id: 1,
    title: '哭戏名场面，演技炸裂！',
    needPay: false
  },
  {
    id: 2,
    title: '高甜瞬间混剪，太上头了',
    needPay: true
  },
  {
    id: 3,
    title: '经典台词合集',
    needPay: true
  },
  {
    id: 4,
    title: '幕后花絮大放送',
    needPay: true
  }
])

// 是否有付费内容
const hasPaidContent = ref(true)

// 评论列表
const commentList = ref([])

// 计算属性：当前显示的评论
const displayedComments = computed(() => {
  return commentList.value.slice(0, displayedCommentsCount.value)
})

// 计算属性：是否还有更多评论
const hasMoreComments = computed(() => {
  return displayedCommentsCount.value < commentList.value.length
})

// 计算属性：剩余评论数量
const remainingCommentsCount = computed(() => {
  return commentList.value.length - displayedCommentsCount.value
})

// 事件处理函数
const onFollowClick = async () => {
  try {
    if (isFollowed.value) {
      // 取消关注
      const response = await api.unfollowActor(actorId.value)
      if (response.code === 200) {
        isFollowed.value = false
        Taro.showToast({
          title: '已取消关注',
          icon: 'success'
        })
      }
    } else {
      // 关注
      const response = await api.followActor(actorId.value)
      if (response.code === 200) {
        isFollowed.value = true
        Taro.showToast({
          title: '关注成功',
          icon: 'success'
        })
      }
    }
  } catch (error) {
    console.error('关注操作失败:', error)
    // 检查是否是认证错误
    if (error.message && error.message.includes('401')) {
      Taro.showToast({
        title: '请先登录',
        icon: 'none'
      })
    } else if (error.message && error.message.includes('400')) {
      Taro.showToast({
        title: '操作失败，数据错误',
        icon: 'none'
      })
    } else {
      Taro.showToast({
        title: `操作失败: ${error.message || '请重试'}`,
        icon: 'none'
      })
    }
  }
}

function onLoadMoreDramas() {
  // 每次加载4个（2行2列）
  displayedDramasCount.value = Math.min(
    displayedDramasCount.value + 4,
    dramaList.value.length
  )
  console.log('加载更多短剧，当前显示:', displayedDramasCount.value)
}

function onDramaClick(drama) {
  console.log('跳转到短剧详情页:', drama)
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/drama/index?title=${encodeURIComponent(drama.title)}`
    })
  }
}

// 整个付费区域点击处理
function onPaidContentClick() {
  console.log('点击付费区域')
  if (typeof Taro !== 'undefined' && Taro.showModal) {
    Taro.showModal({
      title: '解锁精彩内容',
      content: '解锁后可观看所有精彩片段，包括甜蜜告白、经典吻戏等内容，是否立即购买？',
      confirmText: '立即购买',
      cancelText: '稍后再说',
      success: (res) => {
        if (res.confirm) {
          console.log('用户点击立即购买')
          // 这里可以跳转到付费页面
          Taro.showToast({
            title: '跳转到付费页面',
            icon: 'none'
          })
        }
      }
    })
  }
}

function onCutClick(cut) {
  console.log('点击Cut:', cut)

  // 如果有付费内容且当前点击的是付费内容，不处理（由遮罩处理）
  if (hasPaidContent.value && cut.needPay) {
    return
  }

  // 免费内容，直接播放
  console.log('播放免费内容:', cut.title)
  // 可以跳转到视频播放页面
}

// 加载更多评论
function onLoadMoreComments() {
  console.log('加载更多评论')
  // 每次增加3条评论
  displayedCommentsCount.value = Math.min(
    displayedCommentsCount.value + 3,
    commentList.value.length
  )
}

// 评论相关功能
function onCommentInputClick() {
  showCommentModal.value = true
}

function onCloseCommentModal() {
  showCommentModal.value = false
}

async function onSubmitComment() {
  if (commentText.value.trim()) {
    try {
      // 调用API发布评论
      const response = await api.postComment({
        targetType: 'actor',
        targetId: actorId.value,
        content: commentText.value.trim()
      })

      if (response.code === 201) {
        // 添加到评论列表顶部
        const newComment = {
          id: response.data.id,
          author: response.data.user?.nickname || response.data.user?.username || '我',
          content: response.data.content,
          time: '刚刚',
          avatar: response.data.user?.avatar || ''
        }
        commentList.value.unshift(newComment)
        totalComments.value++

        // 清空输入框并关闭弹窗
        commentText.value = ''
        showCommentModal.value = false

        // 显示成功提示
        Taro.showToast({
          title: '评论发布成功',
          icon: 'success'
        })
      }
    } catch (error) {
      console.error('发布评论失败:', error)
      let errorMessage = '发布失败，请重试'

      if (error.message && error.message.includes('401')) {
        errorMessage = '请先登录'
      } else if (error.message && error.message.includes('400')) {
        errorMessage = '评论内容有误'
      } else if (error.message) {
        errorMessage = `发布失败: ${error.message}`
      }

      Taro.showToast({
        title: errorMessage,
        icon: 'none'
      })
    }
  }
}
</script>
