<template>
  <view class="page-myta">
    <!-- Header -->
    <view class="header">
      <TabSwitch v-model="currentTab" :tabs="tabOptions" @tab-change="onTabChange" />
      <SearchInput v-model="searchValue" placeholder="搜索演员" />
    </view>

    <!-- Main Content -->
    <view class="main-content">
      <!-- 榜单头部 -->
      <view class="ranking-header">
        <view class="ranking-title">
          <text class="material-icons title-icon">stars</text>
          <text class="title-text">{{ currentTabTitle }}</text>
        </view>
        <text class="update-time">每小时更新</text>
      </view>
      <!-- 榜单内容 -->
      <view class="ranking-list">
        <view class="ranking-item" v-for="(item, idx) in getCurrentTabData()" :key="item.id || item.name"
          @tap="onActorClick(item)">
          <view class="actor-avatar-wrap">
            <image :src="item.avatar" class="actor-avatar" />
            <view class="ranking-badge" :class="getRankingBadgeClass(idx + 1)">
              <text class="ranking-number">{{ idx + 1 }}</text>
            </view>
          </view>
          <view class="actor-info">
            <view class="actor-info-row">
              <view class="actor-info-main">
                <text class="actor-name">{{ item.name }}</text>
                <view class="actor-gender" :class="item.gender === '女' ? 'gender-female' : 'gender-male'">
                  <text class="iconfont" :class="item.gender === '女' ? 'icon-female' : 'icon-male'"></text>
                </view>
              </view>
              <view class="actor-hot">
                <text class="iconfont icon-local_fire_department"></text>
                <text class="actor-hot-num">{{ item.hot }}</text>
              </view>
            </view>
            <text class="actor-desc">代表作：{{ item.works }}</text>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script setup>
import './myta.scss'
import { ref, computed, onMounted } from 'vue'
import SearchInput from '../../components/SearchInput.vue'
import TabSwitch from '../../components/TabSwitch.vue'
import Taro from '@tarojs/taro'
import { api } from '../../utils/api'

// 当前选中的tab索引
const currentTab = ref(1)
const loading = ref(false)
const searchValue = ref('')

// Tab选项
const tabOptions = ref([
  { key: 'follow', label: '关注' },
  { key: 'total', label: '总榜' },
  { key: 'male', label: '男星榜' },
  { key: 'female', label: '女星榜' }
])

// Tab切换事件
function onTabChange(event) {
  console.log('Tab changed:', event)
}

// 跳转到演员页面
function onActorClick(actor) {
  console.log('跳转到演员页面:', actor)
  const actorId = actor.id || actor.name
  const name = actor.name
  if (typeof Taro !== 'undefined' && Taro.navigateTo) {
    Taro.navigateTo({
      url: `/pages/actor/index?id=${actorId}&name=${encodeURIComponent(name)}`
    })
  }
}

// 当前tab标题
const currentTabTitle = computed(() => {
  return tabOptions.value[currentTab.value]?.label || '总榜'
})

// 演员排行数据
const actorRankingData = ref({
  follow: [],    // 关注的演员
  total: [],     // 总榜
  male: [],      // 男星榜
  female: []     // 女星榜
})

// 加载用户关注的演员
const loadUserFollows = async () => {
  try {
    loading.value = true
    const response = await api.getUserFollows()
    if (response.code === 200) {
      actorRankingData.value.follow = response.data.actors || []
    }
  } catch (error) {
    console.error('加载关注数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载演员排行榜
const loadActorRanking = async (type) => {
  try {
    const response = await api.getActorRanking({ type })
    if (response.code === 200) {
      actorRankingData.value[type] = response.data.actors || []
    }
  } catch (error) {
    console.error(`加载${type}排行榜失败:`, error)
  }
}

// 页面加载时获取数据
onMounted(() => {
  loadUserFollows()
  loadActorRanking('total')
  loadActorRanking('male')
  loadActorRanking('female')
})

// 获取当前tab的数据
const getCurrentTabData = () => {
  const tabKeys = ['follow', 'total', 'male', 'female']
  const currentKey = tabKeys[currentTab.value] || 'total'
  return actorRankingData.value[currentKey] || []
}

// 获取排名徽章样式
function getRankingBadgeClass(rank) {
  if (rank === 1) return 'badge-first'
  if (rank === 2) return 'badge-second'
  if (rank === 3) return 'badge-third'
  return 'badge-other'
}



</script>
