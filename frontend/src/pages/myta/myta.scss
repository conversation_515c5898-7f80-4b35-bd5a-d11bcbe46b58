.page-myta {
  min-height: 100vh;
  background-color: #F3F4F6;
  padding-bottom: 120rpx;
  display: flex;
  flex-direction: column;
  position: relative;
  isolation: isolate;

  // 重置可能的样式冲突
  * {
    box-sizing: border-box;
  }
}

.header {
  position: sticky;
  top: 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  z-index: 10;
  padding: 24rpx 32rpx 16rpx 32rpx;
  width: 100%;
  box-sizing: border-box;
}
.main-content {
  flex: 1;
  padding: 24rpx;
  padding-top: 16rpx;
  width: 100%;
  box-sizing: border-box;
  overflow-x: hidden;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;

  .ranking-title {
    display: flex;
    align-items: center;

    .title-icon {
      font-size: 36rpx;
      margin-right: 16rpx;
      color: #EF4444;

      &.material-icons {
        font-family: 'Material Icons';
        font-weight: normal;
        font-style: normal;
        line-height: 1;
        letter-spacing: normal;
        text-transform: none;
        display: inline-block;
        white-space: nowrap;
        word-wrap: normal;
        direction: ltr;
        font-feature-settings: 'liga';
        -webkit-font-feature-settings: 'liga';
        -webkit-font-smoothing: antialiased;
      }
    }

    .title-text {
      font-size: 36rpx;
      font-weight: bold;
      color: #1F2937;
    }
  }

  .update-time {
    font-size: 24rpx;
    color: #9CA3AF;
  }
}
.ranking-list {
  background-color: #FFFFFF;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 12rpx 0 rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1rpx solid #F3F4F6;
  transition: all 0.3s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #F9FAFB;
    border-radius: 16rpx;
    margin: 0 -16rpx;
    padding: 24rpx 16rpx;
  }
}
.actor-avatar-wrap {
  position: relative;
  width: 96rpx;
  height: 96rpx;
  flex-shrink: 0;
  margin-right: 24rpx;
}

.actor-avatar {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  background: #E5E7EB;
  border: 2rpx solid #FFFFFF;
  box-shadow: 0 2rpx 8rpx 0 rgba(0, 0, 0, 0.1);
}

.ranking-badge {
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3rpx solid #FFFFFF;
  box-shadow: 0 2rpx 4rpx 0 rgba(0, 0, 0, 0.1);

  .ranking-number {
    font-size: 20rpx;
    font-weight: bold;
    color: white;
  }

  &.badge-first {
    background-color: #DC2626;
  }

  &.badge-second {
    background-color: #F59E0B;
  }

  &.badge-third {
    background-color: #EAB308;
  }

  &.badge-other {
    background-color: #6B7280;
  }
}
.actor-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  min-width: 0;
}

.actor-info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.actor-info-main {
  display: flex;
  align-items: center;
  gap: 16rpx;
  flex: 1;
  min-width: 0;
}

.actor-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #1F2937;
  line-height: 1.4;
}

.actor-gender {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 999rpx;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  height: 32rpx;
  flex-shrink: 0;

  &.gender-female {
    background: #FCE7F3;
    color: #EC4899;
  }

  &.gender-male {
    background: #DBEAFE;
    color: #2563EB;
  }
}

.actor-hot {
  display: flex;
  align-items: center;
  gap: 6rpx;
  color: #EF4444;
  font-size: 28rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.actor-hot-num {
  font-size: 28rpx;
  font-weight: bold;
}

.actor-desc {
  font-size: 26rpx;
  color: #6B7280;
  line-height: 1.5;
  word-wrap: break-word;
  overflow-wrap: break-word;
}

// 图标样式
.icon-female::before {
  content: "female";
}

.icon-male::before {
  content: "male";
}

.icon-local_fire_department::before {
  content: "local_fire_department";
  font-size: 28rpx;
}
.iconfont {
  font-family: 'Material Icons', 'iconfont', sans-serif;
}

// 响应式设计
@media (max-width: 375px) {
  .main-content {
    padding: 16rpx !important;
  }

  .ranking-list {
    padding: 24rpx !important;
  }

  .ranking-item {
    padding: 20rpx 0 !important;

    &:hover {
      margin: 0 -12rpx !important;
      padding: 20rpx 12rpx !important;
    }
  }

  .actor-avatar-wrap {
    width: 80rpx !important;
    height: 80rpx !important;
    margin-right: 20rpx !important;
  }

  .ranking-badge {
    width: 28rpx !important;
    height: 28rpx !important;
    top: -6rpx !important;
    left: -6rpx !important;

    .ranking-number {
      font-size: 18rpx !important;
    }
  }

  .actor-info {
    gap: 8rpx !important;
  }

  .actor-info-main {
    gap: 12rpx !important;
  }

  .actor-name {
    font-size: 28rpx !important;
  }

  .actor-gender {
    font-size: 18rpx !important;
    height: 28rpx !important;
    padding: 2rpx 10rpx !important;
  }

  .actor-hot {
    font-size: 24rpx !important;
    gap: 4rpx !important;
  }

  .actor-hot-num {
    font-size: 24rpx !important;
  }

  .actor-desc {
    font-size: 22rpx !important;
  }

  .icon-local_fire_department::before {
    font-size: 24rpx !important;
  }
}