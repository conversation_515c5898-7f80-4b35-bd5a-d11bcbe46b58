# 环境配置指南

本项目支持多环境构建，包括开发环境、测试环境和生产环境。

## 环境说明

### 开发环境 (development)
- **API地址**: `http://localhost:8080`
- **特点**: 
  - 不压缩代码，便于调试
  - 保留所有 console 和 debugger
  - 启用热重载
  - 代码分割优化较少

### 测试环境 (test)
- **API地址**: `https://test-52kdj-api.wansu.tech`
- **特点**:
  - 部分代码优化
  - 保留调试信息
  - 不混淆变量名
  - 保留 console 输出

### 生产环境 (production)
- **API地址**: `https://52kdj-api.wansu.tech`
- **特点**:
  - 完全压缩混淆
  - 移除所有 console 和 debugger
  - 启用 Tree Shaking
  - 最大化代码分割优化

## 构建命令

### 微信小程序构建

```bash
# 开发环境构建
npm run build:weapp:dev

# 测试环境构建
npm run build:weapp:test

# 生产环境构建
npm run build:weapp:prod

# 开发环境监听模式
npm run dev:weapp

# 测试环境监听模式
npm run dev:weapp:test
```

### H5构建

```bash
# 开发环境构建
npm run build:h5:dev

# 测试环境构建
npm run build:h5:test

# 生产环境构建
npm run build:h5:prod

# 开发环境监听模式
npm run dev:h5

# 测试环境监听模式
npm run dev:h5:test
```

## 环境变量

在代码中可以通过以下常量获取环境信息：

```javascript
// API基础地址
console.log(API_BASE_URL)

// 当前环境
console.log(NODE_ENV) // 'development' | 'test' | 'production'

// 当前平台
console.log(TARO_ENV) // 'weapp' | 'h5' | 'swan' 等

// 应用版本
console.log(APP_VERSION)

// 是否为微信小程序
console.log(IS_WEAPP) // true | false
```

## 配置文件说明

- `config/index.js` - 主配置文件，包含基础配置和环境选择逻辑
- `config/dev.js` - 开发环境特定配置
- `config/test.js` - 测试环境特定配置
- `config/prod.js` - 生产环境特定配置

## 注意事项

1. **微信小程序发布**：
   - 开发版本使用 `npm run build:weapp:dev`
   - 体验版本使用 `npm run build:weapp:test`
   - 正式版本使用 `npm run build:weapp:prod`

2. **API地址配置**：
   - 确保测试环境API地址 `https://test-52kdj-api.wansu.tech` 可访问
   - 生产环境API地址 `https://52kdj-api.wansu.tech` 为正式服务

3. **调试建议**：
   - 开发阶段使用开发环境构建
   - 提交测试前使用测试环境构建验证
   - 发布前使用生产环境构建确保最终效果

## 环境配置自动生成

项目会根据构建环境自动生成对应的 `project.config.json` 文件：

- **开发环境**: 关闭URL检查，启用热重载，不压缩代码
- **测试环境**: 启用URL检查，关闭热重载，不压缩代码（便于调试）
- **生产环境**: 启用所有检查和优化，压缩代码，启用懒加载

## 快速开始

```bash
# 开发环境（推荐日常开发使用）
npm run dev:weapp

# 测试环境（推荐提测前验证）
npm run build:weapp:test

# 生产环境（推荐发布前构建）
npm run build:weapp:prod
```

## 环境工具类使用

在代码中可以使用 `src/utils/env.js` 提供的工具方法：

```javascript
import { isDev, isTest, isProd, envConfig, logger } from '@/utils/env'

// 环境判断
if (isDev()) {
  // 开发环境特定逻辑
}

// 使用环境配置
console.log(envConfig.apiBaseUrl)

// 使用环境相关的日志工具
logger.debug('这只在开发环境显示')
logger.log('这在开发和测试环境显示')
logger.error('这在所有环境都显示')
```
